import logging
import time
import os
import json
import asyncio
from typing import Dict, Any, List, Optional, AsyncGenerator, Union, Callable
from functools import lru_cache
from pathlib import Path

from openai import OpenAI
from fastapi import FastAPI, HTTPException, Request, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.security import H<PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from mangum import Mangum
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Import authentication module
from .auth.auth import get_current_user, User, create_access_token
# Import authentication middleware
from .auth.middleware import AuthenticationMiddleware
# Import subscription middleware
from .subscription.middleware import SubscriptionMiddleware
# Import routes
from .subscription.routes import router as subscription_router
from .auth.routes import router as auth_router

# Load environment variables
env_path = Path(__file__).resolve().parent.parent / '.env'
load_dotenv(dotenv_path=env_path)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)


class ModelLoader:
    """
    Loads and configures AI models based on environment settings.
    Supports OpenAI, HuggingFace, and DeepSeek providers.
    """

    def __init__(self):
        # Load provider and model settings from environment
        self.provider = os.getenv("AI_PROVIDER", "openai").lower()
        self.temperature = float(os.getenv("MODEL_TEMPERATURE", "0.7"))
        self.max_tokens = int(os.getenv("MODEL_MAX_TOKENS", "500"))

        # Print debug information
        print("=" * 50)
        print("MODEL CONFIGURATION:")
        print(f"Provider: {self.provider}")
        print(f"Temperature: {self.temperature}")
        print(f"Max Tokens: {self.max_tokens}")

        # Configure provider-specific settings
        if self.provider == "openai":
            # Initialize OpenAI client
            self.api_key = os.getenv("OPENAI_API_KEY")
            self.model_name = os.getenv("OPENAI_API_MODEL", "gpt-3.5-turbo")
            self.client = OpenAI(api_key=self.api_key)
            print(f"OpenAI Model: {self.model_name}")
            print(f"OpenAI API Key: {'Set' if self.api_key else 'Not set'}")

        elif self.provider == "deepseek":
            # Initialize DeepSeek client (using OpenAI client with custom base URL)
            self.api_key = os.getenv("DEEPSEEK_API_KEY")
            self.api_base = os.getenv("DEEPSEEK_API_BASE", "https://api.deepseek.com/v1")
            self.model_name = os.getenv("DEEPSEEK_API_MODEL", "deepseek-chat")
            self.client = OpenAI(
                api_key=self.api_key,
                base_url=self.api_base
            )
            print(f"DeepSeek Model: {self.model_name}")
            print(f"DeepSeek API Key: {'Set' if self.api_key else 'Not set'}")
            print(f"DeepSeek API Base: {self.api_base}")

        elif self.provider == "huggingface":
            # For HuggingFace, we'd need to implement a different approach
            # This is a placeholder for future implementation
            self.model_name = os.getenv("HUGGINGFACE_MODEL", "mistralai/Mistral-7B-Instruct-v0.1")
            print(f"HuggingFace Model: {self.model_name}")
            print("HuggingFace integration not fully implemented in this version")

        else:
            # Default to OpenAI if provider is not recognized
            logger.warning(f"Unsupported provider: {self.provider}, defaulting to OpenAI")
            self.provider = "openai"
            self.api_key = os.getenv("OPENAI_API_KEY")
            self.model_name = os.getenv("OPENAI_API_MODEL", "gpt-3.5-turbo")
            self.client = OpenAI(api_key=self.api_key)

        print("=" * 50)

    async def generate_response(self, selected_text: Optional[str], user_intent: Optional[str],
                               tone: str, purpose: str, max_tokens_override: Optional[int] = None) -> str:
        """Generate a response using the configured AI provider."""
        messages = create_prompt(selected_text, user_intent, tone, purpose)

        # Use the max_tokens_override if provided (from subscription tier)
        max_tokens = min(max_tokens_override, self.max_tokens) if max_tokens_override else self.max_tokens
        # Ensure max_tokens is an integer to avoid DeepSeek API errors
        max_tokens = int(max_tokens)

        if self.provider in ["openai", "deepseek"]:
            # Both OpenAI and DeepSeek use the same API format with the new client
            response = await asyncio.to_thread(
                lambda: self.client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    temperature=self.temperature,
                    max_tokens=max_tokens,
                )
            )
            return response.choices[0].message.content.strip()

        elif self.provider == "huggingface":
            # Placeholder for HuggingFace implementation
            # In a real implementation, you would use the HuggingFace API here
            logger.warning("HuggingFace provider not fully implemented")
            return "HuggingFace integration not implemented in this version."

        else:
            # This should not happen due to the check in __init__
            raise ValueError(f"Unsupported provider: {self.provider}")

    async def generate_response_stream(self, selected_text: Optional[str], user_intent: Optional[str],
                                tone: str, purpose: str, max_tokens_override: Optional[int] = None) -> AsyncGenerator[str, None]:
        """Generate a streaming response using the configured AI provider."""
        messages = create_prompt(selected_text, user_intent, tone, purpose)

        # Use the max_tokens_override if provided (from subscription tier)
        max_tokens = min(max_tokens_override, self.max_tokens) if max_tokens_override else self.max_tokens
        # Ensure max_tokens is an integer to avoid DeepSeek API errors
        max_tokens = int(max_tokens)

        if self.provider in ["openai", "deepseek"]:
            # Both OpenAI and DeepSeek use the same streaming API format with the new client
            stream = await asyncio.to_thread(
                lambda: self.client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    temperature=self.temperature,
                    max_tokens=max_tokens,
                    stream=True
                )
            )

            # Process the streaming response
            buffer = ""
            # In the new OpenAI SDK (v1.0.0+), the stream is not an async iterator
            # We need to create an async generator from the synchronous iterator
            async def async_generator():
                for chunk in stream:
                    yield chunk
                    await asyncio.sleep(0)  # Yield control to allow other tasks to run

            async for chunk in async_generator():
                if chunk.choices and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    buffer += content
                    # Send buffer when it reaches a reasonable size or contains sentence-ending punctuation
                    if len(buffer) >= 20 or any(p in buffer for p in ['.', '!', '?', '\n']):
                        buffer = buffer.replace('\n', '<br>').strip()
                        if not buffer:
                            print("No content to send, sending a placeholder...")
                            buffer = "<br>"
                        print(f"Chunk sent: {buffer}")
                        yield f"data: {buffer} \n\n"
                        buffer = ""

            # After the loop, yield whatever is left in the buffer
            if buffer.strip():
                yield f"data: {buffer} \n\n"


        elif self.provider == "huggingface":
            # Placeholder for HuggingFace streaming implementation
            yield f"data: {json.dumps({'text': 'HuggingFace streaming not implemented'})}\n\n"
            yield f"data: {json.dumps({'text': '', 'done': True})}\n\n"

        else:
            # This should not happen due to the check in __init__
            yield f"data: {json.dumps({'error': f'Unsupported provider: {self.provider}'})}\n\n"

# Initialize the model loader
model_loader = ModelLoader()

# Application settings
@lru_cache()
def get_settings():
    """Get application settings from environment variables."""
    return {
        "app_name": os.getenv("APP_NAME", "ReplyPal API"),
        "version": os.getenv("APP_VERSION", "1.0.0"),
        "environment": os.getenv("ENVIRONMENT", "development"),
        "log_level": os.getenv("LOG_LEVEL", "INFO"),
        "cors_origins": os.getenv("CORS_ORIGINS", "*").split(","),
        "request_timeout": int(os.getenv("REQUEST_TIMEOUT", "30")),
        "max_request_size": int(os.getenv("MAX_REQUEST_SIZE", "10000")),
    }

settings = get_settings()

# Pydantic models
class GenerateRequest(BaseModel):
    """Request model for generating AI responses."""
    selected_text: Optional[str] = Field(
        default=None,
        description="Text selected from the webpage"
    )
    user_intent: Optional[str] = Field(
        default=None,
        description="User's intent or instruction for the AI. If null, AI will determine the mood of the message",
        max_length=1000
    )
    tone: Optional[str] = Field(
        default="neutral",
        description="Desired tone of the response (e.g., friendly, formal, empathetic)"
    )
    purpose: Optional[str] = Field(
        default="respond",
        description="Purpose of the response (e.g., ask, apologize, suggest)"
    )

class GenerateResponse(BaseModel):
    """Response model for AI-generated text."""
    response: str = Field(
        ...,
        description="AI-generated response"
    )

class HealthResponse(BaseModel):
    """Response model for health check endpoint."""
    status: str = Field(
        default="ok",
        description="Service status"
    )
    version: str = Field(
        default="1.0.0",
        description="API version"
    )

class TokenRequest(BaseModel):
    """Request model for token generation."""
    uid: str = Field(..., description="User ID from Google authentication")
    email: Optional[str] = Field(None, description="User email")
    name: Optional[str] = Field(None, description="User display name")
    picture: Optional[str] = Field(None, description="User profile picture URL")

class TokenResponse(BaseModel):
    """Response model for token generation."""
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    user: dict = Field(..., description="User information")

# Create FastAPI app
app = FastAPI(
    title=settings["app_name"],
    version=settings["version"],
    description="ReplyPal API for generating smart replies based on selected text and user intent",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for testing
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add authentication middleware (must be before subscription middleware)
app.add_middleware(AuthenticationMiddleware, excluded_paths=[
    "/docs",
    "/redoc",
    "/openapi.json",
    "/ping",
    "/auth/token",
    "/auth/google-login",
    "/auth/google-callback",
])

# Add subscription middleware
app.add_middleware(SubscriptionMiddleware, excluded_paths=[
    "/docs",
    "/redoc",
    "/openapi.json",
    "/ping",
    "/auth/token",
    "/auth/google-login",
    "/auth/google-callback",
    "/subscription/webhook",
    "/subscription/create-checkout-session",
    "/subscription/customer-portal"
])

# Include routes
app.include_router(subscription_router)
app.include_router(auth_router)

# Print CORS settings
print("=" * 50)
print("CORS SETTINGS:")
print(f"Allow Origins: {['*']}")
print(f"Allow Methods: {['*']}")
print(f"Allow Headers: {['*']}")
print("=" * 50)


handler = Mangum(app)



@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Middleware to add processing time header and log requests."""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    logger.info(
        f"Request: {request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Process Time: {process_time:.4f}s"
    )
    return response

def sanitize_input(text: str) -> str:
    """Sanitize user input to prevent injection attacks."""
    if not text:
        return ""
    # Basic sanitization - remove control characters
    sanitized = ''.join(char for char in text if ord(char) >= 32 or char == '\n')
    # Limit length
    max_length = settings["max_request_size"]
    if len(sanitized) > max_length:
        logger.warning(f"Input text truncated from {len(sanitized)} to {max_length} characters")
        sanitized = sanitized[:max_length]
    return sanitized



def create_prompt(selected_text: Optional[str], user_intent: Optional[str], tone: str, purpose: str) -> List[Dict[str, str]]:
    """Create a prompt for the OpenAI API based on the request parameters."""

    base_instructions = [
        "You are ReplyPal, an AI assistant that helps users craft direct responses to messages.",
        "Respond ONLY with the message content that should be sent to the other person or exactly to the point.",
        "Do NOT include any commentary, explanations, titles, instructions, or formatting like 'Subject:' or 'Here's a template...'.",
        "If the selected content is an email, add the email signature and any boilerplate text",
        "If the purpose is 'reply', respond directly to the selected text.",
        "If the purpose is 'rewrite', rewrite the selected text.",
        "If the purpose is 'summarize', summarize the selected text.",
        "If the purpose is 'explain', explain the selected text.",
        "If the purpose is 'thank', thank the sender.",
        "If the purpose is 'apologize', apologize to the sender.",
        "If the purpose is 'suggest', suggest an action to the sender.",
        "If the purpose is 'ask', ask questions based on the selected text.",
        "No pretext or posttext. No additional questions.",
        "Be concise and to the point.",
        f"Tone: {tone}.",
        "If the tone is 'emoji', include appropriate emojis in the response."
    ]

    if user_intent and selected_text:
        # When intent is present, it overrides purpose
        system_message = "\n".join(base_instructions + [
            "Act based on the user's intent towards the selected text.",
            "Ignore purpose."
        ])
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": f"Selected text: {selected_text}"},
            {"role": "user", "content": f"My intent: {user_intent}"}
        ]
    elif user_intent and not selected_text:
        # When intent is present, it overrides purpose
        system_message = "\n".join(base_instructions + [
            "Act based on the user's intent.",
            "Ignore purpose."
        ])
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": f"My intent: {user_intent}"}
        ]
    else:
        # Fall back to purpose if intent is missing
        system_message = "\n".join(base_instructions + [
            f"Purpose: {purpose}.",
            "If no user intent is provided, determine the appropriate response based on the selected text."
        ])
        messages = [
            {"role": "system", "content": system_message}
        ]
        if selected_text:
            messages.append({"role": "user", "content": f"Selected text: {selected_text}"})
        messages.append({"role": "user", "content": "Please determine the appropriate response based on the selected text."})

    print(f"messages: {messages}")
    return messages

@app.post("/auth/token", response_model=TokenResponse, tags=["Authentication"])
async def login_for_access_token(request: TokenRequest):
    """Generate a JWT token for the authenticated user."""
    print("=" * 50)
    print(f"TOKEN REQUEST AT: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"USER ID: {request.uid}")
    print(f"USER EMAIL: {request.email}")
    print("=" * 50)

    # Create user data for token
    user_data = {
        "sub": request.uid,  # JWT subject is the user ID
        "email": request.email,
        "name": request.name
    }

    # Create access token
    access_token = create_access_token(user_data)

    # Return token and user info
    return TokenResponse(
        access_token=access_token,
        token_type="bearer",
        user={
            "uid": request.uid,
            "email": request.email,
            "name": request.name,
            "picture": request.picture
        }
    )

@app.get("/ping", response_model=HealthResponse, tags=["Health"])
async def health_check():
    """Health check endpoint to verify the API is running."""
    print("=" * 50)
    print("PING ENDPOINT CALLED")
    print("=" * 50)
    return HealthResponse(status="ok", version=settings["version"])

@app.get("/test", tags=["Debug"])
async def test_endpoint():
    """Test endpoint that returns a simple response without calling any external APIs."""
    print("=" * 50)
    print("TEST ENDPOINT CALLED")
    print("=" * 50)
    return {"status": "ok", "message": "Test endpoint is working correctly"}

@app.options("/generate", tags=["Debug"])
async def options_generate():
    """Handle OPTIONS request for the generate endpoint."""
    print("=" * 50)
    print("OPTIONS /generate ENDPOINT CALLED")
    print("=" * 50)
    return JSONResponse(content={})

@app.post("/generate", response_model=GenerateResponse, tags=["Generation"])
async def generate_response(
    request: GenerateRequest,
    background_tasks: BackgroundTasks,
    request_obj: Request,
    current_user: User = Depends(get_current_user)  # Require authentication
):
    """Generate a smart reply based on selected text and user intent."""
    # Get user from request state if available (set by middleware)
    if hasattr(request_obj.state, "user"):
        current_user = request_obj.state.user

    # Log raw request details
    print("=" * 50)
    print(f"RECEIVED REQUEST AT: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"CLIENT IP: {request_obj.client.host}")
    print(f"USER: {current_user.uid} ({current_user.email})")
    print(f"REQUEST HEADERS: {dict(request_obj.headers)}")
    print(f"REQUEST BODY: {request}")
    print("=" * 50)

    try:
        # Sanitize inputs
        selected_text = sanitize_input(request.selected_text) if request.selected_text else None
        user_intent = sanitize_input(request.user_intent) if request.user_intent else None
        tone = sanitize_input(request.tone) if request.tone else "neutral"
        purpose = sanitize_input(request.purpose) if request.purpose else "respond"

        # Log sanitized inputs
        user_intent_log = user_intent[:50] + "..." if user_intent and len(user_intent) > 50 else user_intent
        if user_intent:
            logger.info(f"Generating response for intent: {user_intent_log}")
        else:
            logger.info("Generating response with mood detection (no user intent provided)")

        # Set timeout for model response
        timeout = settings["request_timeout"]

        try:
            # Get token limit from user's subscription
            from .subscription.middleware import get_token_limit
            token_limit = await get_token_limit(current_user.uid)

            # Generate response with timeout using the model loader
            response_text = await asyncio.wait_for(
                model_loader.generate_response(
                    selected_text=selected_text,
                    user_intent=user_intent,
                    tone=tone,
                    purpose=purpose,
                    max_tokens_override=token_limit
                ),
                timeout=timeout
            )

            # Log successful generation (async in background)
            background_tasks.add_task(
                logger.info,
                f"Generated response of length {len(response_text)} characters"
            )

            # Record usage synchronously to ensure it's counted before the next request
            from .subscription.middleware import record_usage
            usage_recorded, limit_exceeded = await record_usage(
                current_user.uid,
                "/generate",
                100  # For simplicity, using a fixed token count
            )
            if not usage_recorded:
                logger.warning(f"Failed to record usage for user {current_user.uid}. This may affect usage limits.")

            # Check if user has exceeded their limit
            if limit_exceeded:
                logger.warning(f"User {current_user.uid} has exceeded their daily usage limit. Returning error response.")
                raise HTTPException(
                    status_code=429,
                    detail="You have exceeded your daily usage limit. Please upgrade your subscription."
                )

            return GenerateResponse(response=response_text)

        except asyncio.TimeoutError:
            logger.error(f"Response generation timed out after {timeout} seconds")
            raise HTTPException(
                status_code=504,
                detail=f"Response generation timed out after {timeout} seconds"
            )

    except HTTPException as e:
        # Re-raise HTTP exceptions with their original status code
        logger.error(f"HTTP exception in response: {str(e.detail)} (status code: {e.status_code})")
        raise e
    except Exception as e:
        logger.error(f"Error generating response: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating response: {str(e)}"
        )

@app.post("/generate_stream", tags=["Generation"])
async def generate_response_stream(
    request: GenerateRequest,
    request_obj: Request,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)  # Require authentication
):
    """Generate a smart reply based on selected text and user intent, streaming the response token by token."""
    # Get user from request state if available (set by middleware)
    if hasattr(request_obj.state, "user"):
        current_user = request_obj.state.user

    # Log raw request details
    print("=" * 50)
    print(f"RECEIVED STREAMING REQUEST AT: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"CLIENT IP: {request_obj.client.host}")
    print(f"USER: {current_user.uid} ({current_user.email})")
    print(f"REQUEST HEADERS: {dict(request_obj.headers)}")
    print(f"REQUEST BODY: {request}")
    print("=" * 50)

    try:
        # Sanitize inputs
        selected_text = sanitize_input(request.selected_text) if request.selected_text else None
        user_intent = sanitize_input(request.user_intent) if request.user_intent else None
        tone = sanitize_input(request.tone) if request.tone else "neutral"
        purpose = sanitize_input(request.purpose) if request.purpose else "respond"

        # Log sanitized inputs
        user_intent_log = user_intent[:50] + "..." if user_intent and len(user_intent) > 50 else user_intent
        if user_intent:
            logger.info(f"Generating streaming response for intent: {user_intent_log}")
        else:
            logger.info("Generating streaming response with mood detection (no user intent provided)")

        # Get token limit from user's subscription
        from .subscription.middleware import get_token_limit
        token_limit = await get_token_limit(current_user.uid)

        # Record usage synchronously to ensure it's counted before the next request
        from .subscription.middleware import record_usage
        usage_recorded, limit_exceeded = await record_usage(
            current_user.uid,
            "/generate_stream",
            100  # For simplicity, using a fixed token count
        )
        if not usage_recorded:
            logger.warning(f"Failed to record usage for user {current_user.uid}. This may affect usage limits.")

        # Check if user has exceeded their limit
        if limit_exceeded:
            logger.warning(f"User {current_user.uid} has exceeded their daily usage limit. Returning error response.")
            raise HTTPException(
                status_code=429,
                detail="You have exceeded your daily usage limit. Please upgrade your subscription."
            )

        # Create a streaming response using the model loader
        return StreamingResponse(
            model_loader.generate_response_stream(
                selected_text=selected_text,
                user_intent=user_intent,
                tone=tone,
                purpose=purpose,
                max_tokens_override=token_limit
            ),
            media_type="text/event-stream"
        )

    except HTTPException as e:
        # Re-raise HTTP exceptions with their original status code
        logger.error(f"HTTP exception in streaming response: {str(e.detail)} (status code: {e.status_code})")
        raise e
    except Exception as e:
        logger.error(f"Error generating streaming response: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating streaming response: {str(e)}"
        )

# Background task for checking subscriptions that need auto-renewal
@app.on_event("startup")
async def start_subscription_renewal_scheduler():
    """Start the background task for checking subscriptions that need auto-renewal"""
    from .subscription.middleware import check_all_subscriptions_for_renewal

    async def renewal_task():
        """Background task to check for subscriptions that need renewal"""
        while True:
            try:
                logger.info("Running scheduled subscription renewal check")
                await check_all_subscriptions_for_renewal()
                logger.info("Completed subscription renewal check")
            except Exception as e:
                logger.error(f"Error in subscription renewal check: {str(e)}")

            # Run once per day (86400 seconds)
            await asyncio.sleep(86400)

    # Start the background task
    asyncio.create_task(renewal_task())
    logger.info("Started subscription renewal scheduler")


@app.post("/admin/check-renewals", tags=["Admin"])
async def trigger_renewal_check(
    current_user: User = Depends(get_current_user)
):
    """
    Manually trigger the subscription renewal check
    This endpoint is for testing purposes only
    """
    # Only allow admin users to trigger this
    if current_user.email not in ["<EMAIL>", "<EMAIL>"]:
        raise HTTPException(
            status_code=403,
            detail="Only admin users can trigger renewal checks"
        )

    try:
        from .subscription.middleware import check_all_subscriptions_for_renewal
        await check_all_subscriptions_for_renewal()
        return {"status": "success", "message": "Renewal check completed successfully"}
    except Exception as e:
        logger.error(f"Error in manual renewal check: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error in renewal check: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
