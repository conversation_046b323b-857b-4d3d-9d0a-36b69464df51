"""
Database module for ReplyPal API
Handles database connections and operations using DynamoDB
"""

import os
import json
import time
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Union, Any
from pathlib import Path

import boto3
from boto3.dynamodb.conditions import Key, Attr
from botocore.exceptions import ClientError

from .models import StripeCustomer, StripeSubscription, UserSubscription, UsageRecord, SubscriptionStatus, SubscriptionTier

# Custom JSON encoder to handle Decimal types
class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        try:
            return super(DecimalEncoder, self).default(obj)
        except TypeError:
            return str(obj)  # Convert any other non-serializable types to string

# Function to recursively convert Decimal values in a dictionary/list
def convert_decimal_in_dict(obj):
    if isinstance(obj, dict):
        return {k: convert_decimal_in_dict(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_decimal_in_dict(i) for i in obj]
    elif isinstance(obj, Decimal):
        return float(obj)
    else:
        return obj

# AWS DynamoDB settings
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
DYNAMODB_ENDPOINT = os.getenv("DYNAMODB_ENDPOINT")  # For local development

# Table names
CUSTOMERS_TABLE = os.getenv("DYNAMODB_CUSTOMERS_TABLE", "replypal-customers")
SUBSCRIPTIONS_TABLE = os.getenv("DYNAMODB_SUBSCRIPTIONS_TABLE", "replypal-subscriptions")
USER_SUBSCRIPTIONS_TABLE = os.getenv("DYNAMODB_USER_SUBSCRIPTIONS_TABLE", "replypal-user-subscriptions")
USAGE_RECORDS_TABLE = os.getenv("DYNAMODB_USAGE_RECORDS_TABLE", "replypal-usage-records")

# Log AWS configuration (without sensitive data)
print("=" * 50)
print("AWS CONFIGURATION:")
print(f"AWS Profile: sumathy_aws")
print(f"DynamoDB Endpoint: {DYNAMODB_ENDPOINT or 'Default AWS endpoint'}")
print("=" * 50)

# When using a profile, we don't need to explicitly set access key and secret
# The profile will handle authentication
if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY:
    print("WARNING: AWS credentials are set in environment variables but will be ignored in favor of the sumathy_aws profile.")
    print("To use environment variables instead of a profile, remove the profile_name from dynamodb_kwargs.")

# Initialize kwargs for resource (without profile)
dynamodb_kwargs = {}
if DYNAMODB_ENDPOINT:
    dynamodb_kwargs['endpoint_url'] = DYNAMODB_ENDPOINT

try:
    print("Attempting to connect to DynamoDB with the following configuration:")
    print(f"Profile: sumathy_aws")
    print(f"Endpoint: {DYNAMODB_ENDPOINT or 'Default AWS endpoint'}")

    # Create a session with the profile
    session = boto3.Session(profile_name='sumathy_aws')
    # Use the session to create a resource
    dynamodb = session.resource('dynamodb', **dynamodb_kwargs)

    # Test connection by listing tables
    tables = list(dynamodb.tables.all())
    table_names = [table.name for table in tables]
    print(f"Successfully connected to DynamoDB. Available tables: {', '.join(table_names) if table_names else 'No tables found'}")
except Exception as e:
    print(f"ERROR connecting to DynamoDB: {str(e)}")
    print(f"Exception type: {type(e).__name__}")
    print("=" * 50)
    print("UNABLE TO CONNECT TO DYNAMODB")
    print("=" * 50)
    # Set dynamodb to None to indicate we're using no implementation
    dynamodb = None

# Initialize table variables with None as default
customers_table = None
subscriptions_table = None
user_subscriptions_table = None
usage_records_table = None

# Get table references if DynamoDB is available and we're not using mock implementation
if dynamodb:
    try:
        # Check if tables exist before trying to access them
        tables = list(dynamodb.tables.all())
        table_names = [table.name for table in tables]

        # Check required tables
        required_tables = [CUSTOMERS_TABLE, SUBSCRIPTIONS_TABLE, USER_SUBSCRIPTIONS_TABLE, USAGE_RECORDS_TABLE]
        missing_tables = [table for table in required_tables if table not in table_names]

        if missing_tables:
            print(f"WARNING: The following required tables are missing: {', '.join(missing_tables)}")
            print("Tables will be set to None and operations will fail gracefully")

        # Initialize table references
        customers_table = dynamodb.Table(CUSTOMERS_TABLE) if CUSTOMERS_TABLE in table_names else None
        subscriptions_table = dynamodb.Table(SUBSCRIPTIONS_TABLE) if SUBSCRIPTIONS_TABLE in table_names else None
        user_subscriptions_table = dynamodb.Table(USER_SUBSCRIPTIONS_TABLE) if USER_SUBSCRIPTIONS_TABLE in table_names else None
        usage_records_table = dynamodb.Table(USAGE_RECORDS_TABLE) if USAGE_RECORDS_TABLE in table_names else None

        # Log table initialization status
        print(f"DynamoDB tables initialization status:")
        print(f"- {CUSTOMERS_TABLE}: {'Initialized' if customers_table else 'Not initialized'}")
        print(f"- {SUBSCRIPTIONS_TABLE}: {'Initialized' if subscriptions_table else 'Not initialized'}")
        print(f"- {USER_SUBSCRIPTIONS_TABLE}: {'Initialized' if user_subscriptions_table else 'Not initialized'}")
        print(f"- {USAGE_RECORDS_TABLE}: {'Initialized' if usage_records_table else 'Not initialized'}")

        # If any table is not initialized, log a warning
        if not all([customers_table, subscriptions_table, user_subscriptions_table, usage_records_table]):
            print("Some tables are not initialized. Operations on these tables will fail gracefully.")
    except Exception as e:
        print(f"ERROR initializing DynamoDB tables: {str(e)}")
        print(f"Exception type: {type(e).__name__}")
        print("=" * 50)
        print("UNABLE TO INITIALIZE DYNAMODB TABLES")
        print("=" * 50)
        # Tables are already initialized to None by default


# Helper function to convert datetime to ISO format for DynamoDB
def datetime_to_iso(dt: datetime) -> str:
    """Convert datetime to ISO format string for DynamoDB"""
    return dt.isoformat()

# Helper function to convert ISO format string to datetime
def iso_to_datetime(iso_str: str) -> datetime:
    """Convert ISO format string to datetime"""
    return datetime.fromisoformat(iso_str)

# Database operations for Stripe customers
async def create_stripe_customer(customer: StripeCustomer) -> Dict[str, Any]:
    """Create a new Stripe customer in the database"""
    customer_dict = customer.model_dump()
    customer_dict["created"] = datetime_to_iso(datetime.now())

    try:
        customers_table.put_item(Item=customer_dict)
        return customer_dict
    except Exception as e:
        print(f"Error creating customer: {str(e)}")
        raise


async def get_stripe_customer(customer_id: str) -> Optional[Dict[str, Any]]:
    """Get a Stripe customer by ID"""
    try:
        response = customers_table.get_item(Key={"id": customer_id})
        return response.get("Item")
    except Exception as e:
        print(f"Error getting customer: {str(e)}")
        return None


async def get_stripe_customer_by_user_id(user_id: str) -> Optional[Dict[str, Any]]:
    """Get a Stripe customer by user ID"""
    try:
        # First get the user subscription to find the customer ID
        response = user_subscriptions_table.get_item(Key={"user_id": user_id})
        user_subscription = response.get("Item")

        if not user_subscription:
            return None

        # Then get the customer using the customer ID
        customer_id = user_subscription.get("stripe_customer_id")
        if not customer_id:
            return None

        return await get_stripe_customer(customer_id)
    except Exception as e:
        print(f"Error getting customer by user ID: {str(e)}")
        return None


# Database operations for Stripe subscriptions
async def create_stripe_subscription(subscription: StripeSubscription) -> Dict[str, Any]:
    """Create a new Stripe subscription in the database"""
    subscription_dict = subscription.model_dump()
    subscription_dict["created"] = datetime_to_iso(datetime.now())

    # Convert datetime objects to ISO strings
    if isinstance(subscription_dict.get("current_period_start"), datetime):
        subscription_dict["current_period_start"] = datetime_to_iso(subscription_dict["current_period_start"])

    if isinstance(subscription_dict.get("current_period_end"), datetime):
        subscription_dict["current_period_end"] = datetime_to_iso(subscription_dict["current_period_end"])

    try:
        subscriptions_table.put_item(Item=subscription_dict)
        return subscription_dict
    except Exception as e:
        print(f"Error creating subscription: {str(e)}")
        raise


async def update_stripe_subscription(subscription_id: str, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Update a Stripe subscription"""
    try:
        # First get the existing subscription
        response = subscriptions_table.get_item(Key={"id": subscription_id})
        subscription = response.get("Item")

        if not subscription:
            return None

        # Add updated timestamp
        update_data["updated"] = datetime_to_iso(datetime.now())

        # Convert datetime objects to ISO strings
        for key, value in update_data.items():
            if isinstance(value, datetime):
                update_data[key] = datetime_to_iso(value)

        # Prepare update expression and attribute values
        update_expression = "SET "
        expression_attribute_values = {}

        for i, (key, value) in enumerate(update_data.items()):
            update_expression += f"#{i} = :{i}"
            if i < len(update_data) - 1:
                update_expression += ", "
            expression_attribute_values[f":{i}"] = value

        # Prepare expression attribute names
        expression_attribute_names = {f"#{i}": key for i, key in enumerate(update_data.keys())}

        # Update the item
        response = subscriptions_table.update_item(
            Key={"id": subscription_id},
            UpdateExpression=update_expression,
            ExpressionAttributeNames=expression_attribute_names,
            ExpressionAttributeValues=expression_attribute_values,
            ReturnValues="ALL_NEW"
        )

        return response.get("Attributes")
    except Exception as e:
        print(f"Error updating subscription: {str(e)}")
        return None


async def get_stripe_subscription(subscription_id: str) -> Optional[Dict[str, Any]]:
    """Get a Stripe subscription by ID"""
    try:
        response = subscriptions_table.get_item(Key={"id": subscription_id})
        return response.get("Item")
    except Exception as e:
        print(f"Error getting subscription: {str(e)}")
        return None


# Database operations for user subscriptions
async def create_user_subscription(user_subscription: UserSubscription) -> Dict[str, Any]:
    """Create a new user subscription in the database"""
    subscription_dict = user_subscription.model_dump()
    now = datetime_to_iso(datetime.now())
    subscription_dict["created"] = now
    subscription_dict["updated"] = now

    # Check if the table is available
    if user_subscriptions_table is None:
        print("WARNING: user_subscriptions_table is None. DynamoDB connection may not be initialized.")
        # Return the subscription without storing it
        return subscription_dict

    try:
        user_subscriptions_table.put_item(Item=subscription_dict)
        return subscription_dict
    except Exception as e:
        print(f"Error creating user subscription: {str(e)}")
        print(f"Exception type: {type(e).__name__}")
        # Return the subscription without raising an exception
        return subscription_dict


async def update_user_subscription(user_id: str, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Update a user subscription"""
    try:
        # First get the existing subscription
        response = user_subscriptions_table.get_item(Key={"user_id": user_id})
        subscription = response.get("Item")

        if not subscription:
            return None

        # Add updated timestamp
        update_data["updated"] = datetime_to_iso(datetime.now())

        # Convert any nested objects to dictionaries
        for key, value in update_data.items():
            if hasattr(value, "model_dump"):
                update_data[key] = value.model_dump()

        # Prepare update expression and attribute values
        update_expression = "SET "
        expression_attribute_values = {}

        for i, (key, value) in enumerate(update_data.items()):
            update_expression += f"#{i} = :{i}"
            if i < len(update_data) - 1:
                update_expression += ", "
            expression_attribute_values[f":{i}"] = value

        # Prepare expression attribute names
        expression_attribute_names = {f"#{i}": key for i, key in enumerate(update_data.keys())}

        # Update the item
        response = user_subscriptions_table.update_item(
            Key={"user_id": user_id},
            UpdateExpression=update_expression,
            ExpressionAttributeNames=expression_attribute_names,
            ExpressionAttributeValues=expression_attribute_values,
            ReturnValues="ALL_NEW"
        )

        return response.get("Attributes")
    except Exception as e:
        print(f"Error updating user subscription: {str(e)}")
        return None


async def get_user_subscription(user_id: str) -> Optional[Dict[str, Any]]:
    """Get a user subscription by user ID"""
    # If the table is not available, return None
    if user_subscriptions_table is None:
        print(f"WARNING: user_subscriptions_table is None. Returning None for user {user_id}")
        return None

    try:
        response = user_subscriptions_table.get_item(Key={"user_id": user_id})
        item = response.get("Item")

        # Convert any Decimal values in the item
        if item:
            item = convert_decimal_in_dict(item)

        return item
    except Exception as e:
        print(f"Error getting user subscription: {str(e)}")
        print(f"Exception type: {type(e).__name__}")
        return None


async def get_all_user_subscriptions() -> List[Dict[str, Any]]:
    """Get all user subscriptions"""
    # If the table is not available, return empty list
    if user_subscriptions_table is None:
        print("WARNING: user_subscriptions_table is None. Returning empty list for all user subscriptions")
        return []

    try:
        # Scan the table to get all subscriptions
        response = user_subscriptions_table.scan()
        items = response.get("Items", [])

        # Handle pagination if there are more items
        while "LastEvaluatedKey" in response:
            response = user_subscriptions_table.scan(
                ExclusiveStartKey=response["LastEvaluatedKey"]
            )
            items.extend(response.get("Items", []))

        # Convert any Decimal values in the items
        items = convert_decimal_in_dict(items)

        print(f"Retrieved {len(items)} user subscriptions")
        return items
    except Exception as e:
        print(f"Error getting all user subscriptions: {str(e)}")
        print(f"Exception type: {type(e).__name__}")
        return []


# Database operations for usage records
async def create_usage_record(usage_record: UsageRecord) -> Dict[str, Any]:
    """Create a new usage record in the database"""
    record_dict = usage_record.model_dump()

    # Ensure we have a current timestamp
    current_time = datetime.now()
    record_dict["timestamp"] = datetime_to_iso(current_time)

    # Generate a unique ID for the record
    record_dict["id"] = str(uuid.uuid4())

    print(f"DEBUG: Creating usage record for user {record_dict.get('user_id')} at {record_dict['timestamp']}")
    print(f"DEBUG: Record details: {json.dumps(record_dict, cls=DecimalEncoder)}")

    # Check if the table is available
    if usage_records_table is None:
        print("WARNING: usage_records_table is None. DynamoDB connection may not be initialized.")
        # Return the record without storing it
        return record_dict

    try:
        # Put the item in DynamoDB
        result = usage_records_table.put_item(Item=record_dict)
        print(f"DEBUG: Successfully created usage record with ID {record_dict['id']}")
        print(f"DEBUG: DynamoDB response: {result}")

        # Verify the record was created by retrieving it
        try:
            verification = usage_records_table.get_item(Key={"id": record_dict["id"]})
            if "Item" in verification:
                print(f"DEBUG: Verified record exists in DynamoDB")
            else:
                print(f"WARNING: Could not verify record exists in DynamoDB")
        except Exception as verify_error:
            print(f"WARNING: Error verifying record: {str(verify_error)}")

        return record_dict
    except Exception as e:
        print(f"ERROR creating usage record: {str(e)}")
        print(f"Exception type: {type(e).__name__}")
        # Return the record without raising an exception
        return record_dict


async def get_user_usage(user_id: str, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
    """Get usage records for a user within a time range"""
    try:
        # DynamoDB doesn't support direct querying by timestamp range without a GSI
        # For simplicity, we'll scan the table and filter client-side
        response = usage_records_table.scan(
            FilterExpression=Key("user_id").eq(user_id)
        )

        items = response.get("Items", [])

        # Filter by timestamp range client-side
        start_iso = datetime_to_iso(start_time)
        end_iso = datetime_to_iso(end_time)

        filtered_items = [
            item for item in items
            if item.get("timestamp") and start_iso <= item["timestamp"] <= end_iso
        ]

        return filtered_items
    except Exception as e:
        print(f"Error getting user usage: {str(e)}")
        return []


async def get_daily_usage_count(user_id: str) -> int:
    """Get the number of requests made by a user today"""
    # If the table is not available, return the maximum limit to prevent unlimited access
    if usage_records_table is None:
        print(f"WARNING: usage_records_table is None. Returning maximum limit for daily usage count for user {user_id}")
        # Return a high value (e.g., 999) to prevent unlimited access when DB is not available
        return 999

    try:
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_start_iso = datetime_to_iso(today_start)

        print(f"DEBUG: Checking usage for user {user_id} since {today_start_iso}")

        # DynamoDB doesn't support direct counting without scanning
        # For simplicity, we'll scan the table and filter client-side
        response = usage_records_table.scan(
            FilterExpression=Key("user_id").eq(user_id)
        )

        items = response.get("Items", [])
        print(f"DEBUG: Found {len(items)} total usage records for user {user_id}")

        # Print all timestamps for debugging
        for item in items:
            timestamp = item.get("timestamp", "NO_TIMESTAMP")
            request_type = item.get("request_type", "NO_TYPE")
            print(f"DEBUG: Record - timestamp: {timestamp}, type: {request_type}")

        # Convert any Decimal values in the items
        items = convert_decimal_in_dict(items)

        # Filter by today's date client-side and count
        today_records = [item for item in items if item.get("timestamp") and item["timestamp"] >= today_start_iso]
        count = len(today_records)

        print(f"DEBUG: Filtered to {count} records for today (since {today_start_iso})")
        print(f"User {user_id} has made {count} requests today (since {today_start_iso})")

        # Print today's records for debugging
        for item in today_records:
            timestamp = item.get("timestamp", "NO_TIMESTAMP")
            request_type = item.get("request_type", "NO_TYPE")
            print(f"DEBUG: Today's record - timestamp: {timestamp}, type: {request_type}")

        return count
    except Exception as e:
        print(f"Error getting daily usage count: {str(e)}")
        print(f"Exception type: {type(e).__name__}")
        # Return a high value to prevent unlimited access when DB operations fail
        print(f"WARNING: Returning maximum limit for user {user_id} due to DB error")
        return 999
