"""
Authentication routes for ReplyPal API
Handles Google OAuth login and authentication
"""

import os
import json
import secrets
from typing import Dict, Optional, Any
from urllib.parse import urlencode

import requests
from fastapi import APIRouter, Request, HTTPException, status, Depends
from fastapi.responses import RedirectResponse, JSONResponse
from pydantic import BaseModel

from .auth import create_access_token, User

router = APIRouter(prefix="/auth", tags=["Authentication"])

# Google OAuth configuration
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID", "************-hntko3euueak8f49h8lrsgq8k4os2cp3.apps.googleusercontent.com")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET", "")
GOOGLE_REDIRECT_URI = os.getenv("GOOGLE_REDIRECT_URI", "http://localhost:8000/auth/google-callback")
GOOGLE_AUTH_URL = "https://accounts.google.com/o/oauth2/auth"
GOOGLE_TOKEN_URL = "https://oauth2.googleapis.com/token"
GOOGLE_USER_INFO_URL = "https://www.googleapis.com/oauth2/v1/userinfo"

# Store state tokens temporarily (in a real app, use Redis or another storage)
state_tokens = {}

class GoogleCallbackParams(BaseModel):
    """Query parameters for Google OAuth callback"""
    code: str
    state: str
    scope: Optional[str] = None
    authuser: Optional[str] = None
    prompt: Optional[str] = None


@router.get("/google-login")
async def google_login(redirect_uri: str):
    """
    Initiate Google OAuth login flow

    Args:
        redirect_uri: URL to redirect to after successful login
    """
    # Generate a random state token to prevent CSRF
    state = secrets.token_urlsafe(32)

    # Store the state token and redirect URI
    state_tokens[state] = redirect_uri

    # Build the Google OAuth URL with the recommended flow
    params = {
        "client_id": GOOGLE_CLIENT_ID,
        "redirect_uri": GOOGLE_REDIRECT_URI,
        "response_type": "code",
        "scope": "email profile",
        "state": state,
        "access_type": "offline",
        "prompt": "consent",
        # Add these parameters to avoid the loopback error
        "include_granted_scopes": "true"
    }

    auth_url = f"{GOOGLE_AUTH_URL}?{urlencode(params)}"

    # Redirect to Google OAuth
    return RedirectResponse(url=auth_url)


@router.get("/google-callback")
async def google_callback(request: Request):
    """
    Handle Google OAuth callback

    This endpoint is called by Google after the user authorizes the app
    """
    # Get query parameters
    params = dict(request.query_params)

    # Validate state token to prevent CSRF
    state = params.get("state")
    if not state or state not in state_tokens:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid state token"
        )

    # Get the redirect URI associated with this state token
    redirect_uri = state_tokens.pop(state)

    # Get authorization code
    code = params.get("code")
    if not code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Authorization code not provided"
        )

    try:
        # Exchange authorization code for access token
        token_data = await exchange_code_for_token(code)

        # Get user info using the access token
        user_info = await get_user_info(token_data["access_token"])

        # Create a JWT token for the user
        user_data = {
            "sub": user_info["id"],  # Use Google ID as user ID
            "email": user_info["email"],
            "name": user_info["name"]
        }

        access_token = create_access_token(user_data)

        # Prepare user data for the frontend
        user = {
            "id": user_info["id"],
            "email": user_info["email"],
            "name": user_info["name"],
            "picture": user_info.get("picture"),
            "apiToken": access_token
        }

        # Add user data to the redirect URI as a fragment
        user_fragment = f"#user={json.dumps(user)}"
        final_redirect_url = f"{redirect_uri}{user_fragment}"

        # Log the successful authentication
        print(f"Google OAuth successful for user: {user_info['email']}")
        print(f"Redirecting to: {redirect_uri}")

        # Redirect to the frontend with the user data
        return RedirectResponse(url=final_redirect_url)

    except Exception as e:
        # Log the error
        print(f"Error in Google callback: {str(e)}")

        # Redirect to the frontend with an error
        error_redirect = f"{redirect_uri}#error={str(e)}"
        return RedirectResponse(url=error_redirect)


async def exchange_code_for_token(code: str) -> Dict[str, Any]:
    """
    Exchange authorization code for access token

    Args:
        code: Authorization code from Google

    Returns:
        Token data including access_token, refresh_token, etc.
    """
    payload = {
        "client_id": GOOGLE_CLIENT_ID,
        "client_secret": GOOGLE_CLIENT_SECRET,
        "code": code,
        "grant_type": "authorization_code",
        "redirect_uri": GOOGLE_REDIRECT_URI
    }

    response = requests.post(GOOGLE_TOKEN_URL, data=payload)

    if response.status_code != 200:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to exchange code for token: {response.text}"
        )

    return response.json()


async def get_user_info(access_token: str) -> Dict[str, Any]:
    """
    Get user info from Google

    Args:
        access_token: Google OAuth access token

    Returns:
        User info including id, email, name, picture, etc.
    """
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(GOOGLE_USER_INFO_URL, headers=headers)

    if response.status_code != 200:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get user info: {response.text}"
        )

    return response.json()
