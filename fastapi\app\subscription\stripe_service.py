"""
Stripe integration module for ReplyPal API
Handles Stripe API calls and webhook events
"""

import os
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Union, Any

import stripe
from fastapi import HTTPException, status

from ..models import (
    StripeCustomer,
    StripeSubscription,
    UserSubscription,
    UsageLimit,
    SubscriptionStatus,
    SubscriptionTier
)
from .. import database as db
from .settings import TIER_LIMITS, STRIPE_BASIC_PRICE_ID

# Initialize Stripe with API key
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
STRIPE_WEBHOOK_SECRET = os.getenv("STRIPE_WEBHOOK_SECRET")

# Subscription plan IDs
SUBSCRIPTION_PLANS = {
    SubscriptionTier.FREE: None,  # Free tier doesn't have a Stripe price ID
    SubscriptionTier.BASIC: STRIPE_BASIC_PRICE_ID
}


async def create_stripe_customer(user_id: str, email: str, name: Optional[str] = None) -> StripeCustomer:
    """Create a new customer in Stripe"""
    try:
        # Check if customer already exists for this user
        existing_customer = await db.get_stripe_customer_by_user_id(user_id)
        if existing_customer:
            return StripeCustomer(**existing_customer)

        # Create customer in Stripe
        customer = stripe.Customer.create(
            email=email,
            name=name,
            metadata={"user_id": user_id}
        )

        # Create customer in database
        stripe_customer = StripeCustomer(
            id=customer.id,
            email=customer.email,
            name=customer.name,
            metadata={"user_id": user_id}
        )
        await db.create_stripe_customer(stripe_customer)

        # Create user subscription with free tier
        user_subscription = UserSubscription(
            user_id=user_id,
            stripe_customer_id=customer.id,
            tier=SubscriptionTier.FREE,
            status=SubscriptionStatus.ACTIVE,
            usage_limits=TIER_LIMITS[SubscriptionTier.FREE]
        )
        await db.create_user_subscription(user_subscription)

        return stripe_customer

    except stripe.error.StripeError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error creating Stripe customer: {str(e)}"
        )


async def create_subscription(
    user_id: str,
    customer_id: str,
    price_id: str,
    payment_method_id: Optional[str] = None
) -> StripeSubscription:
    """Create a new subscription in Stripe"""
    try:
        # If payment method provided, attach it to customer
        if payment_method_id:
            stripe.PaymentMethod.attach(
                payment_method_id,
                customer=customer_id
            )

            # Set as default payment method
            stripe.Customer.modify(
                customer_id,
                invoice_settings={
                    "default_payment_method": payment_method_id
                }
            )

        # Create subscription
        subscription = stripe.Subscription.create(
            customer=customer_id,
            items=[{"price": price_id}],
            expand=["latest_invoice.payment_intent"],
            metadata={"user_id": user_id}
        )

        # Determine tier from price ID
        tier = next(
            (tier for tier, price in SUBSCRIPTION_PLANS.items() if price == price_id),
            SubscriptionTier.BASIC  # Default to basic if not found
        )

        # Create subscription in database
        stripe_subscription = StripeSubscription(
            id=subscription.id,
            customer_id=customer_id,
            status=subscription.status,
            tier=tier,
            current_period_start=datetime.fromtimestamp(subscription.current_period_start),
            current_period_end=datetime.fromtimestamp(subscription.current_period_end),
            cancel_at_period_end=subscription.cancel_at_period_end,
            metadata={"user_id": user_id}
        )
        await db.create_stripe_subscription(stripe_subscription)

        # Update user subscription
        current_time = datetime.now()
        await db.update_user_subscription(
            user_id=user_id,
            update_data={
                "stripe_subscription_id": subscription.id,
                "tier": tier,
                "status": subscription.status,
                "usage_limits": TIER_LIMITS[tier].dict(),
                "total_tokens_used": 0,  # Reset token usage counter on subscription creation/renewal
                "last_renewal_date": current_time  # Set renewal date to now
            }
        )

        return stripe_subscription

    except stripe.error.StripeError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error creating subscription: {str(e)}"
        )


async def cancel_subscription(subscription_id: str) -> Dict[str, Any]:
    """Cancel a subscription in Stripe"""
    try:
        subscription = stripe.Subscription.delete(subscription_id)

        # Update subscription in database
        await db.update_stripe_subscription(
            subscription_id=subscription_id,
            update_data={"status": subscription.status}
        )

        # Get user ID from subscription metadata
        db_subscription = await db.get_stripe_subscription(subscription_id)
        if db_subscription and "user_id" in db_subscription.get("metadata", {}):
            user_id = db_subscription["metadata"]["user_id"]

            # Update user subscription
            await db.update_user_subscription(
                user_id=user_id,
                update_data={
                    "status": subscription.status,
                    "tier": SubscriptionTier.FREE,
                    "usage_limits": TIER_LIMITS[SubscriptionTier.FREE].dict()
                }
            )

        return {"id": subscription_id, "status": subscription.status}

    except stripe.error.StripeError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error canceling subscription: {str(e)}"
        )


async def handle_webhook_event(payload: bytes, signature: str) -> Dict[str, Any]:
    """Handle Stripe webhook events"""
    try:
        event = stripe.Webhook.construct_event(
            payload, signature, STRIPE_WEBHOOK_SECRET
        )

        event_data = event.data.object

        # Handle different event types
        if event.type == "customer.subscription.created":
            await handle_subscription_created(event_data)
        elif event.type == "customer.subscription.updated":
            await handle_subscription_updated(event_data)
        elif event.type == "customer.subscription.deleted":
            await handle_subscription_deleted(event_data)
        elif event.type == "invoice.payment_succeeded":
            await handle_payment_succeeded(event_data)
        elif event.type == "invoice.payment_failed":
            await handle_payment_failed(event_data)

        return {"status": "success", "event_type": event.type}

    except (stripe.error.SignatureVerificationError, ValueError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid webhook signature: {str(e)}"
        )


async def handle_subscription_created(subscription: stripe.Subscription) -> None:
    """Handle subscription.created webhook event"""
    # This is handled by our create_subscription function
    pass


async def handle_subscription_updated(subscription: stripe.Subscription) -> None:
    """Handle subscription.updated webhook event"""
    # Update subscription in database
    await db.update_stripe_subscription(
        subscription_id=subscription.id,
        update_data={
            "status": subscription.status,
            "current_period_start": datetime.fromtimestamp(subscription.current_period_start),
            "current_period_end": datetime.fromtimestamp(subscription.current_period_end),
            "cancel_at_period_end": subscription.cancel_at_period_end
        }
    )

    # Get user ID from subscription metadata
    db_subscription = await db.get_stripe_subscription(subscription.id)
    if db_subscription and "user_id" in db_subscription.get("metadata", {}):
        user_id = db_subscription["metadata"]["user_id"]

        # Update user subscription status
        await db.update_user_subscription(
            user_id=user_id,
            update_data={"status": subscription.status}
        )


async def handle_subscription_deleted(subscription: stripe.Subscription) -> None:
    """Handle subscription.deleted webhook event"""
    # Update subscription in database
    await db.update_stripe_subscription(
        subscription_id=subscription.id,
        update_data={"status": subscription.status}
    )

    # Get user ID from subscription metadata
    db_subscription = await db.get_stripe_subscription(subscription.id)
    if db_subscription and "user_id" in db_subscription.get("metadata", {}):
        user_id = db_subscription["metadata"]["user_id"]

        # Update user subscription to free tier
        await db.update_user_subscription(
            user_id=user_id,
            update_data={
                "status": subscription.status,
                "tier": SubscriptionTier.FREE,
                "stripe_subscription_id": None,
                "usage_limits": TIER_LIMITS[SubscriptionTier.FREE].dict()
            }
        )


async def handle_payment_succeeded(invoice: stripe.Invoice) -> None:
    """Handle invoice.payment_succeeded webhook event"""
    if invoice.subscription:
        # Update subscription status to active if it was past_due
        subscription = stripe.Subscription.retrieve(invoice.subscription)

        # Update subscription in database
        await db.update_stripe_subscription(
            subscription_id=subscription.id,
            update_data={
                "status": "active",
                "current_period_start": datetime.fromtimestamp(subscription.current_period_start),
                "current_period_end": datetime.fromtimestamp(subscription.current_period_end)
            }
        )

        # Get user ID from subscription metadata
        db_subscription = await db.get_stripe_subscription(subscription.id)
        if db_subscription and "user_id" in db_subscription.get("metadata", {}):
            user_id = db_subscription["metadata"]["user_id"]

            # Get the subscription tier
            tier = db_subscription.get("tier", SubscriptionTier.BASIC.value)

            # Update user subscription - reset token usage counter and update renewal date
            current_time = datetime.now()
            await db.update_user_subscription(
                user_id=user_id,
                update_data={
                    "status": "active",
                    "total_tokens_used": 0,  # Reset token usage counter on payment success
                    "last_renewal_date": current_time  # Update renewal date
                }
            )


async def handle_payment_failed(invoice: stripe.Invoice) -> None:
    """Handle invoice.payment_failed webhook event"""
    if invoice.subscription:
        # Update subscription status
        subscription = stripe.Subscription.retrieve(invoice.subscription)
        await db.update_stripe_subscription(
            subscription_id=subscription.id,
            update_data={"status": subscription.status}
        )

        # Get user ID from subscription metadata
        db_subscription = await db.get_stripe_subscription(subscription.id)
        if db_subscription and "user_id" in db_subscription.get("metadata", {}):
            user_id = db_subscription["metadata"]["user_id"]

            # Update user subscription status
            await db.update_user_subscription(
                user_id=user_id,
                update_data={"status": subscription.status}
            )
