"""
Authentication middleware for ReplyPal API
Sets the authenticated user in the request state for other middlewares
"""

import logging
import time
import json
from decimal import Decimal
from fastapi import Request, status
from fastapi.responses import JSONResponse, PlainTextResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from .auth import verify_token, User

# Configure logging
logger = logging.getLogger(__name__)

# Custom JSON encoder to handle Decimal types
class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)  # Convert Decimal to float for JSON serialization
        # Handle other non-serializable types
        try:
            return super().default(obj)
        except TypeError:
            return str(obj)  # Convert any other non-serializable types to string

# Function to recursively convert Decimal values in a dictionary/list
def convert_decimal_in_dict(obj):
    if isinstance(obj, dict):
        return {k: convert_decimal_in_dict(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_decimal_in_dict(i) for i in obj]
    elif isinstance(obj, Decimal):
        return float(obj)
    else:
        return obj

# Custom JSONResponse that handles Decimal types
class CustomJSONResponse(JSONResponse):
    def render(self, content):
        # First convert any Decimal values in the content
        content = convert_decimal_in_dict(content)

        try:
            return json.dumps(
                content,
                ensure_ascii=False,
                allow_nan=False,
                indent=None,
                separators=(",", ":"),
                cls=DecimalEncoder,
            ).encode("utf-8")
        except TypeError as e:
            # If serialization still fails, log the error and return a simplified response
            logger.error(f"JSON serialization error: {str(e)}")
            return json.dumps(
                {"detail": "An error occurred processing the response"},
                ensure_ascii=False,
                allow_nan=False,
                indent=None,
                separators=(",", ":"),
            ).encode("utf-8")

# Security scheme
security = HTTPBearer(auto_error=False)


class AuthenticationMiddleware:
    """Middleware to authenticate users and set user in request state"""

    def __init__(
        self,
        app,
        excluded_paths: list = None
    ):
        self.app = app
        self.excluded_paths = excluded_paths or [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/ping",
            "/auth/token",
        ]

    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            # If not an HTTP request, just pass through
            await self.app(scope, receive, send)
            return

        # Create a Starlette Request object
        request = Request(scope, receive=receive)

        # Skip middleware for excluded paths
        if any(request.url.path.startswith(path) for path in self.excluded_paths):
            await self.app(scope, receive, send)
            return

        # Extract authorization header
        authorization = request.headers.get("Authorization")

        # Log request path and authorization header
        logger.info(f"Auth middleware processing request to: {request.url.path}")
        logger.info(f"Authorization header present: {authorization is not None}")

        # If no authorization header, return 401 for protected endpoints
        if not authorization:
            # Only return 401 for endpoints that require authentication
            if request.url.path in ["/generate", "/generate_stream", "/subscription/"]:
                logger.warning(f"No authorization header for protected endpoint: {request.url.path}")
                # Ensure we're using CustomJSONResponse with proper error handling
                try:
                    response = CustomJSONResponse(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        content={
                            "detail": "Not authenticated"
                        },
                        headers={"WWW-Authenticate": "Bearer"},
                        media_type="application/json"
                    )
                    await response(scope, receive, send)
                except Exception as e:
                    logger.error(f"Error creating response: {str(e)}")
                    # Fallback to a simpler response if CustomJSONResponse fails
                    fallback = PlainTextResponse(
                        content="Authentication error. Please try again.",
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        headers={"WWW-Authenticate": "Bearer"}
                    )
                    await fallback(scope, receive, send)
                return
            else:
                # For other endpoints, continue without authentication
                logger.info(f"Skipping auth for non-protected endpoint: {request.url.path}")
                await self.app(scope, receive, send)
                return

        try:
            # Parse the authorization header
            scheme, token = authorization.split()
            if scheme.lower() != "bearer":
                logger.warning(f"Invalid authentication scheme: {scheme}")
                raise ValueError("Invalid authentication scheme")

            # Verify the token
            logger.info(f"Verifying token: {token[:10]}...")
            token_data = verify_token(token)
            logger.info(f"Token verified successfully for user: {token_data.uid}")

            # Create user object
            user = User(
                uid=token_data.uid,
                email=token_data.email,
                name=token_data.name
            )

            # Set user in request state for other middlewares
            request.state.user = user
            logger.info(f"User set in request state: {user.uid}")

            # Continue with the request
            logger.info(f"Continuing with authenticated request to: {request.url.path}")
            await self.app(scope, receive, send)

        except Exception as e:
            # If token verification fails, return 401
            logger.error(f"Authentication error: {str(e)}")
            logger.error(f"Exception type: {type(e).__name__}")

            # Prepare a safe error message
            if "Decimal is not JSON serializable" in str(e):
                error_message = "Authentication error occurred. Please try again."
                logger.error("Decimal serialization error detected. Using generic error message.")
            else:
                error_message = str(e)

            try:
                # Create a response with proper error handling
                response_content = {
                    "detail": f"Invalid authentication credentials: {error_message}"
                }

                # Convert any Decimal values in the response content
                response_content = convert_decimal_in_dict(response_content)

                response = CustomJSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content=response_content,
                    headers={"WWW-Authenticate": "Bearer"},
                    media_type="application/json"
                )
                await response(scope, receive, send)
            except Exception as response_error:
                # If creating the response fails, use a simple PlainTextResponse as fallback
                logger.error(f"Error creating response: {str(response_error)}")
                fallback = PlainTextResponse(
                    content="Authentication error. Please try again.",
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    headers={"WWW-Authenticate": "Bearer"}
                )
                await fallback(scope, receive, send)
            return
