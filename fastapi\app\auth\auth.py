"""
Authentication module for ReplyPal API
Handles JWT token creation, validation, and user authentication
"""

import os
import time
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Optional, Union

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from pydantic import BaseModel

# JWT Settings
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-for-jwt-please-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 365 * 10  # 10 years (effectively non-expiring for extension use)

# Security scheme
security = HTTPBearer()

class TokenData(BaseModel):
    """Token data model"""
    uid: str
    email: Optional[str] = None
    name: Optional[str] = None


class User(BaseModel):
    """User model"""
    uid: str
    email: Optional[str] = None
    name: Optional[str] = None
    picture: Optional[str] = None


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a new JWT access token

    Args:
        data: Data to encode in the token
        expires_delta: Token expiration time

    Returns:
        JWT token string
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.now() + expires_delta
    else:
        expire = datetime.now() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

    return encoded_jwt


def verify_token(token: str) -> TokenData:
    """
    Verify a JWT token

    Args:
        token: JWT token string

    Returns:
        TokenData object with user information

    Raises:
        HTTPException: If token is invalid
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])

        uid = payload.get("sub")
        if uid is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Force cast everything to str if not None to avoid Decimal serialization issues
        uid = str(uid)
        email = str(payload.get("email")) if payload.get("email") is not None else None
        name = str(payload.get("name")) if payload.get("name") is not None else None

        # Convert any other potential Decimal values in the payload
        for key in payload:
            if isinstance(payload[key], Decimal):
                payload[key] = float(payload[key])
            elif isinstance(payload[key], dict):
                # Recursively convert nested dictionaries
                for nested_key in payload[key]:
                    if isinstance(payload[key][nested_key], Decimal):
                        payload[key][nested_key] = float(payload[key][nested_key])

        return TokenData(uid=uid, email=email, name=name)

    except JWTError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid authentication credentials: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        # Handle any other exceptions, including potential Decimal serialization issues
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication error: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """
    Get the current user from the token

    Args:
        credentials: HTTP Authorization credentials

    Returns:
        User object

    Raises:
        HTTPException: If token is invalid
    """
    token_data = verify_token(credentials.credentials)

    user = User(
        uid=token_data.uid,
        email=token_data.email,
        name=token_data.name
    )

    return user
